<template>
  <div class="edit-pet-container">
    <div class="page-header">
      <h1 class="page-title">
        <i class="fas fa-edit"></i>
        编辑宠物信息
      </h1>
      <p class="page-subtitle">修改宠物的详细信息</p>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <div v-else-if="pet" class="edit-form-container">
      <el-form
        ref="petFormRef"
        :model="petForm"
        :rules="petRules"
        label-width="120px"
        class="pet-form"
        @submit.prevent="updatePet"
      >
        <!-- 基本信息 -->
        <el-card class="form-section" header="基本信息">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="宠物名称" prop="name">
                <el-input v-model="petForm.name" placeholder="请输入宠物名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="种类" prop="species">
                <el-select v-model="petForm.species" placeholder="请选择种类" style="width: 100%">
                  <el-option label="狗" value="狗" />
                  <el-option label="猫" value="猫" />
                  <el-option label="兔" value="兔" />
                  <el-option label="鸟" value="鸟" />
                  <el-option label="其他" value="其他" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="品种" prop="breed">
                <el-input v-model="petForm.breed" placeholder="请输入品种" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="年龄" prop="age">
                <el-input-number
                  v-model="petForm.age"
                  :min="0"
                  :max="30"
                  placeholder="请输入年龄"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="性别" prop="gender">
                <el-radio-group v-model="petForm.gender">
                  <el-radio label="雄性">雄性</el-radio>
                  <el-radio label="雌性">雌性</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="体重(kg)" prop="weight">
                <el-input-number
                  v-model="petForm.weight"
                  :min="0.1"
                  :max="100"
                  :precision="1"
                  placeholder="请输入体重"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="颜色" prop="color">
                <el-input v-model="petForm.color" placeholder="请输入颜色" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否绝育" prop="isNeutered">
                <el-switch v-model="petForm.isNeutered" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
        
        <div class="form-row">
          <div class="form-group">
            <label>年龄</label>
            <input type="number" v-model="pet.age" min="0" max="30" required>
          </div>
          <div class="form-group">
            <label>性别</label>
            <select v-model="pet.gender" required>
              <option value="雄性">雄性</option>
              <option value="雌性">雌性</option>
            </select>
          </div>
        </div>
        
        <div class="form-group">
          <label>健康状况</label>
          <input type="text" v-model="pet.health_status" required>
        </div>
        
        <div class="form-group">
          <label>描述</label>
          <textarea v-model="pet.description" rows="4" required></textarea>
        </div>
        
        <div class="form-group">
          <label>领养状态</label>
          <select v-model="pet.is_adopted">
            <option :value="false">待领养</option>
            <option :value="true">已被领养</option>
          </select>
        </div>
        
        <div class="form-actions">
          <button type="submit" class="btn btn-primary" :disabled="submitting">
            <i class="fas fa-save"></i>
            {{ submitting ? '更新中...' : '更新信息' }}
          </button>
          <router-link to="/manage" class="btn btn-outline">
            <i class="fas fa-times"></i>
            取消
          </router-link>
        </div>
      </form>
    </div>
    
    <!-- 加载状态 -->
    <div v-else class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>加载中...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { usePetsStore } from '@/stores/pets'

const router = useRouter()
const route = useRoute()
const petsStore = usePetsStore()

// 状态
const loading = ref(true)
const submitting = ref(false)
const pet = ref(null)

// 宠物ID
const petId = route.params.id

// 获取宠物详情
const fetchPet = async () => {
  try {
    loading.value = true
    const result = await petsStore.fetchPetDetail(petId)
    
    if (result.success && result.data) {
      pet.value = { ...result.data }
    } else {
      ElMessage.error('获取宠物信息失败')
      router.push('/manage')
    }
  } catch (error) {
    console.error('获取宠物详情失败:', error)
    ElMessage.error('获取宠物信息失败')
    router.push('/manage')
  } finally {
    loading.value = false
  }
}

// 更新宠物信息
const updatePet = async () => {
  if (!pet.value) return
  
  try {
    submitting.value = true
    
    const result = await petsStore.updatePet(petId, pet.value)
    
    if (result.success) {
      ElMessage.success('宠物信息已更新！')
      router.push('/manage')
    } else {
      ElMessage.error(result.message || '更新失败')
    }
  } catch (error) {
    console.error('更新宠物失败:', error)
    ElMessage.error('更新失败')
  } finally {
    submitting.value = false
  }
}

onMounted(() => {
  fetchPet()
})
</script>

<style scoped>
.edit-pet-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.card {
  background: white;
  border-radius: 10px;
  box-shadow: var(--card-shadow);
  padding: 2rem;
}

.page-title {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: var(--secondary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-title i {
  color: var(--accent);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: flex;
  gap: 1rem;
}

.form-row .form-group {
  flex: 1;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--secondary);
}

input, select, textarea {
  width: 100%;
  padding: 0.8rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border 0.3s;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(78, 137, 174, 0.2);
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;
}

.btn {
  padding: 0.8rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
  font-size: 1rem;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--accent);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #e55a57;
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  border: 1px solid #ddd;
  color: #666;
}

.btn-outline:hover {
  background: #f5f5f5;
  border-color: #999;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-spinner {
  text-align: center;
  color: #666;
}

.loading-spinner i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .edit-pet-container {
    padding: 1rem;
  }
  
  .card {
    padding: 1.5rem;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
