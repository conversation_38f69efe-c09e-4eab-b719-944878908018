package com.petadoption.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * 密码哈希生成工具
 * 用于生成BCrypt密码哈希
 * 
 * <AUTHOR> Team
 */
public class PasswordHashGenerator {
    
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        
        // 生成admin123的哈希
        String password = "admin123";
        String hash = encoder.encode(password);
        
        System.out.println("=================================");
        System.out.println("密码哈希生成完成");
        System.out.println("=================================");
        System.out.println("原始密码: " + password);
        System.out.println("BCrypt哈希: " + hash);
        System.out.println("=================================");
        
        // 验证哈希是否正确
        boolean matches = encoder.matches(password, hash);
        System.out.println("验证结果: " + (matches ? "✓ 正确" : "✗ 错误"));
        System.out.println("=================================");
        
        // 测试现有的哈希
        String existingHash = "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/8G";
        boolean existingMatches = encoder.matches(password, existingHash);
        System.out.println("现有哈希验证: " + (existingMatches ? "✓ 正确" : "✗ 错误"));
        System.out.println("现有哈希: " + existingHash);
        System.out.println("=================================");
    }
}
